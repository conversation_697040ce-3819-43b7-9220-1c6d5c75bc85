<template>
  <el-container class="w-full h-full flex-auto gap-[45px] max-h-[100vh_-_146.5px] overflow-y-auto">
    <div class="w-[292px] h-full border" />

    <el-main class="page-container !pr-[38px] !pb-[47px] !p-0 !flex flex-col gap-[5px]">
      <page-header></page-header>

      <bfMap class="flex-auto"></bfMap>
    </el-main>
  </el-container>
</template>

<script setup lang="ts">
  import bfMap from '@/layouts/BfMap.vue'
</script>
