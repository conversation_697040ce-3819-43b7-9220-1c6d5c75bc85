<script setup lang="ts">
  import { computed } from 'vue'
  import { useI18n } from 'vue-i18n'
  import { formatDmrIdLabel } from '@/utils/bfutil'
  import { ref } from 'vue'

  // todo: add more types, like poc ...
  export type DispatchContactTerminalType = 'sdcTerminal' | 'networkTerminal'
  export type DispatchContactDynamicGroupType = 'taskGroup' | 'tempGroup'
  export type DispatchContactGroupType = 'group' | DispatchContactDynamicGroupType
  export type DispatchContactType = DispatchContactTerminalType | DispatchContactGroupType | 'fullCallContact'

  export type DispatchContactCardProps = {
    // 卡片联系人类型
    type: DispatchContactType
    // 所属单位名称
    parentOrg?: string
    // 名称
    name: string
    dmrIDHex: string
  }

  //  定义组件的props
  const props = withDefaults(defineProps<DispatchContactCardProps>(), {
    type: 'sdcTerminal',
  })

  export type DispatchContactCardEmit = {
    locate: [targetDmrId: string]
    call: [targetDmrId: string]
    hangup: [targetDmrId: string]
    message: [targetDmrId: string]
    sendCommand: [targetDmrId: string]
    sendMessage: [targetDmrId: string]
  }
  const emit = defineEmits<DispatchContactCardEmit>()

  const { t } = useI18n()

  const isTerminal = computed(() => {
    return props.type === 'sdcTerminal' || props.type === 'networkTerminal'
  })

  const iconClass = computed(() => {
    if (isTerminal.value) {
      return 'bfdx-duijiangji'
    } else {
      return 'bfdx-jiqun'
    }
  })

  const displayName = computed(() => {
    return props.parentOrg ? `${props.parentOrg}/${props.name}` : props.name
  })

  const isCalling = ref<boolean>(false)

  const handleLeftBtnClick = () => {
    if (isTerminal.value) {
      emit('locate', props.dmrIDHex)
    } else {
      emit('call', props.dmrIDHex)
    }
  }

  const handleCenterBtnClick = () => {
    if (!isTerminal.value) {
      return
    }
    if (isCalling.value) {
      emit('hangup', props.dmrIDHex)
      isCalling.value = false
    } else {
      emit('call', props.dmrIDHex)
      isCalling.value = true
    }
  }

  const handleRightBtnClick = () => {
    if (isTerminal.value) {
      emit('sendCommand', props.dmrIDHex)
    } else {
      emit('sendMessage', props.dmrIDHex)
    }
  }
</script>

<template>
  <div class="card-container relative w-[205px] h-[109px]" :class="{ 'is-calling': isCalling }">
    <div class="card-header absolute flex items-center justify-center w-[72px] h-[27px] p-[6px] top-0 right-0">
      <slot name="title">
        <ellipsis-text :content="t(`dispatch.contactCard.${props.type}`)" />
      </slot>
    </div>
    <div v-if="isCalling" class="calling-notify-wrapper absolute w-[205px] h-[16px] top-[5px] right-0 flex justify-center">
      <div class="calling-notify w-[70px] h-[14px]">{{ t('dispatch.contactCard.calling') }}</div>
    </div>
    <div class="card-icon bf-iconfont absolute w-[35px] h-[35px] top-[15px] left-[15px]" :class="iconClass"></div>
    <div class="card-content relative flex flex-col items-center justify-end gap-[10px] p-[10px] h-full">
      <div class="relative text-center w-full flex flex-col items-center gap-[4px]">
        <ellipsis-text class="text-[12px] font-bold text-white w-[100px]" not-wfull :content="displayName" />
        <ellipsis-text class="text-[10px] text-white ml-auto w-[100px]" not-wfull :content="formatDmrIdLabel(props.dmrIDHex)" />
      </div>
      <div class="card-btn-group h-[24px] flex items-end justify-around w-full px-[4px] gap-[5px]">
        <div class="left-btn" @click="handleLeftBtnClick">
          {{ isTerminal ? t('dispatch.contactCard.locate') : isCalling ? t('dispatch.contactCard.hangup') : t('dispatch.contactCard.call') }}
        </div>
        <div v-if="isTerminal" class="center-btn" @click="handleCenterBtnClick">
          {{ isCalling ? t('dispatch.contactCard.hangup') : t('dispatch.contactCard.call') }}
        </div>
        <div class="right-btn" @click="handleRightBtnClick">
          {{ isTerminal ? t('dispatch.contactCard.command') : t('dispatch.contactCard.message') }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .card-container {
    background: url('@/assets/images/dispatch/contact_card/contact_bg.svg') no-repeat center center;
    background-size: contain;
    .card-header {
      font-size: 8px;
      font-weight: normal;
      color: #fff;
      text-align: center;
      background: url('@/assets/images/dispatch/contact_card/title_bg.svg') no-repeat center center;
      background-size: contain;
    }
    .calling-notify {
      background-image: linear-gradient(
        to right,
        rgba($color: #e83f25, $alpha: 0) 0%,
        rgba($color: #f2431f, $alpha: 0.45) 15%,
        #ff4817 49%,
        rgba($color: #ec2d16, $alpha: 0.46) 79%,
        rgba($color: #dc1515, $alpha: 0) 100%
      );
      border-image: linear-gradient(to right, rgba($color: #ff4040, $alpha: 0) 0%, #ff2d2d 51%, (rgba($color: #ff1e1e, $alpha: 0)) 100%);
      border-block-width: 1px;
      font-size: 8px;
      line-height: 16px;
      text-align: center;
    }
    .card-icon {
      font-size: 35px;
      line-height: 1;
      &.bfdx-duijiangji:before {
        color: var(--bf-primary-blue-color);
      }
      &.bfdx-jiqun:before {
        background: linear-gradient(#fbe33c, #f9b139);
        background-clip: text;
        color: transparent;
      }
    }
    .card-btn-group {
      .left-btn,
      .center-btn,
      .right-btn {
        width: 56px;
        font-size: 11px;
        line-height: 24px;
        text-align: center;
        color: #fff;
        text-shadow: 0px 1px 3px rgba(0, 131, 255, 0.501961);
        height: 24px;

        cursor: pointer;
        &:active {
          opacity: 0.5;
        }
      }
      .left-btn {
        background: url('@/assets/images/dispatch/contact_card/left_btn_bg.svg') no-repeat center center;
        background-size: contain;
      }
      .center-btn {
        background: url('@/assets/images/dispatch/contact_card/center_btn_bg.svg') no-repeat center center;
        background-size: contain;

        line-height: 20px;
        height: 20px;
      }
      .right-btn {
        background: url('@/assets/images/dispatch/contact_card/right_btn_bg.svg') no-repeat center center;
        background-size: contain;
      }
    }

    &.is-calling {
      filter: drop-shadow(0px 0px 15px #ff9200);

      .center-btn {
        background-image: url('@/assets/images/dispatch/contact_card/center_btn_bg_active.svg');
        color: #ff3939;
        text-shadow: none;
      }
    }
  }
</style>
