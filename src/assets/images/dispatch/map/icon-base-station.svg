<svg width="108" height="149" viewBox="0 0 108 149" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1772_1187)">
<path d="M54 102.5C68.752 102.5 82.0351 105.161 91.5742 109.4C96.3457 111.521 100.115 114.007 102.67 116.692C105.218 119.37 106.5 122.174 106.5 125C106.5 127.826 105.218 130.63 102.67 133.308C100.115 135.993 96.3457 138.479 91.5742 140.6C82.0351 144.839 68.752 147.5 54 147.5C39.248 147.5 25.9649 144.839 16.4258 140.6C11.6543 138.479 7.88539 135.993 5.33008 133.308C2.78222 130.63 1.5 127.826 1.5 125C1.5 122.174 2.78222 119.37 5.33008 116.692C7.88539 114.007 11.6543 111.521 16.4258 109.4C25.9649 105.161 39.248 102.5 54 102.5Z" fill="url(#paint0_linear_1772_1187)" stroke="url(#paint1_linear_1772_1187)" stroke-width="3"/>
<ellipse cx="53.9992" cy="124.11" rx="38.5714" ry="17.7778" fill="#FFEBC9" fill-opacity="0.11"/>
<path d="M53.999 107.833C64.4839 107.833 73.9035 109.794 80.6455 112.902C87.5153 116.068 91.0701 120.14 91.0703 124.11C91.0703 128.08 87.5156 132.152 80.6455 135.319C73.9035 138.426 64.4839 140.388 53.999 140.388C43.5142 140.388 34.0945 138.426 27.3525 135.319C20.4826 132.152 16.9277 128.08 16.9277 124.11C16.928 120.14 20.4829 116.068 27.3525 112.902C34.0945 109.794 43.5142 107.833 53.999 107.833Z" stroke="#FFEBC9" stroke-opacity="0.211765" stroke-width="3"/>
<g filter="url(#filter0_f_1772_1187)">
<ellipse cx="54.0042" cy="124.112" rx="9.64286" ry="8.88889" fill="#FFCD35" fill-opacity="0.36"/>
</g>
<g filter="url(#filter1_f_1772_1187)">
<ellipse cx="54" cy="123.5" rx="15" ry="7.5" fill="white" fill-opacity="0.86"/>
</g>
</g>
<g clip-path="url(#clip1_1772_1187)">
<mask id="path-6-inside-1_1772_1187" fill="white">
<path d="M52.6084 33.5127C63.785 33.5129 72.8428 42.8901 72.8428 54.4541C72.8427 66.0258 63.785 75.4031 52.6084 75.4033C41.4326 75.4033 32.3751 66.0259 32.375 54.4541C32.375 42.89 41.4322 33.5127 52.6084 33.5127Z"/>
</mask>
<g filter="url(#filter2_i_1772_1187)">
<path d="M52.6084 33.5127C63.785 33.5129 72.8428 42.8901 72.8428 54.4541C72.8427 66.0258 63.785 75.4031 52.6084 75.4033C41.4326 75.4033 32.3751 66.0259 32.375 54.4541C32.375 42.89 41.4322 33.5127 52.6084 33.5127Z" fill="url(#paint2_linear_1772_1187)"/>
</g>
<path d="M52.6084 33.5127L52.6085 30.5127H52.6084V33.5127ZM72.8428 54.4541L75.8428 54.4541V54.4541H72.8428ZM52.6084 75.4033V78.4033H52.6085L52.6084 75.4033ZM32.375 54.4541H29.375V54.4541L32.375 54.4541ZM52.6084 33.5127L52.6083 36.5127C62.0316 36.5129 69.8428 44.4488 69.8428 54.4541H72.8428H75.8428C75.8428 41.3315 65.5384 30.513 52.6085 30.5127L52.6084 33.5127ZM72.8428 54.4541L69.8428 54.4541C69.8427 64.4679 62.0308 72.4031 52.6083 72.4033L52.6084 75.4033L52.6085 78.4033C65.5391 78.4031 75.8427 67.5837 75.8428 54.4541L72.8428 54.4541ZM52.6084 75.4033V72.4033C43.1868 72.4033 35.3751 64.4682 35.375 54.4541L32.375 54.4541L29.375 54.4541C29.3751 67.5836 39.6783 78.4033 52.6084 78.4033V75.4033ZM32.375 54.4541H35.375C35.375 44.4485 43.1858 36.5127 52.6084 36.5127V33.5127V30.5127C39.6786 30.5127 29.375 41.3315 29.375 54.4541H32.375Z" fill="url(#paint3_linear_1772_1187)" mask="url(#path-6-inside-1_1772_1187)"/>
<circle cx="52.6055" cy="54.7644" r="21" fill="#D8A05B" fill-opacity="0.360784"/>
<path d="M52.6084 33.5127C63.785 33.5129 72.8428 42.8901 72.8428 54.4541C72.8427 66.0258 63.785 75.4031 52.6084 75.4033C41.4326 75.4033 32.3751 66.0259 32.375 54.4541C32.375 42.89 41.4322 33.5127 52.6084 33.5127Z" fill="url(#paint4_linear_1772_1187)"/>
<g filter="url(#filter3_di_1772_1187)">
<path d="M99.6504 54.5C99.6504 80.1812 78.8316 101 53.1504 101C27.4692 101 6.65039 80.1812 6.65039 54.5C6.65039 28.8188 27.4692 8 53.1504 8C78.8316 8 99.6504 28.8188 99.6504 54.5Z" fill="#166CDA"/>
<path style="mix-blend-mode:overlay" d="M99.6504 54.5C99.6504 80.1812 78.8316 101 53.1504 101C27.4692 101 6.65039 80.1812 6.65039 54.5C6.65039 28.8188 27.4692 8 53.1504 8C78.8316 8 99.6504 28.8188 99.6504 54.5Z" fill="url(#paint5_linear_1772_1187)" fill-opacity="0.58"/>
</g>
<path d="M53.1504 92C73.8611 92 90.6504 75.2107 90.6504 54.5C90.6504 33.7893 73.8611 17 53.1504 17C32.4397 17 15.6504 33.7893 15.6504 54.5C15.6504 75.2107 32.4397 92 53.1504 92Z" fill="#CDFAFF"/>
<g clip-path="url(#clip2_1772_1187)">
<path d="M52.9841 84.6752C36.2626 84.396 22.8804 70.8124 23.1525 54.3913C23.4246 37.8041 37.1601 24.4078 53.6201 24.7541C70.1967 25.1004 83.1937 38.1999 83.1513 54.939C83.1124 71.5227 69.2462 84.9437 52.9841 84.6752Z" fill="white"/>
<path d="M53.3525 27.3376C68.4166 27.366 80.7532 39.755 80.7461 54.8474C80.7389 69.9221 68.3842 82.2267 53.2529 82.2302C38.0087 82.2335 25.6519 69.911 25.6943 54.741C25.7369 39.5886 38.1294 27.3094 53.3525 27.3376ZM53.1465 46.5583C52.2995 46.5536 51.4731 46.8215 50.7891 47.324C50.1051 47.8264 49.599 48.5364 49.3457 49.3494C49.0931 50.1631 49.1075 51.0373 49.3857 51.8425C49.664 52.6478 50.1916 53.3423 50.8916 53.823L44.2246 72.2429C44.0914 72.6149 44.1104 73.0251 44.2764 73.3835C44.4423 73.7418 44.7426 74.0192 45.1113 74.156C45.8806 74.4363 46.7304 74.0353 47.0127 73.2615L48.4248 69.364H57.7803L59.2236 73.3582C59.4778 74.0572 60.2473 74.4157 60.9424 74.1638L61.2109 74.0642C61.906 73.8086 62.2621 73.0349 62.0117 72.3357L55.3242 53.8474C56.7711 52.8747 57.4171 51.0678 56.916 49.3923C56.6718 48.5765 56.1736 47.8605 55.4951 47.3503C54.8167 46.8403 53.9934 46.5631 53.1465 46.5583ZM56.8203 66.7126H49.3887L50.3477 64.0574H55.8604L56.8203 66.7126ZM54.9004 61.4021H51.3076L53.1045 56.4421L54.9004 61.4021ZM66.9922 41.2517C66.74 41.1788 66.4732 41.1739 66.2188 41.238C65.7 41.3694 65.2941 41.7706 65.1494 42.2888C65.0083 42.807 65.1524 43.3611 65.5264 43.741C69.1401 47.4683 69.0949 53.4248 65.4248 57.0955C65.2863 57.2336 65.1759 57.3979 65.1006 57.5789C65.0253 57.7598 64.986 57.954 64.9854 58.1501C64.9847 58.3463 65.0226 58.5409 65.0967 58.7224C65.1707 58.9037 65.2796 59.0688 65.417 59.2078C65.5543 59.3471 65.7185 59.4582 65.8984 59.5339C66.0782 59.6095 66.271 59.6485 66.4658 59.6492C66.6607 59.6498 66.8539 59.6122 67.0342 59.5378C67.2146 59.4633 67.3793 59.353 67.5176 59.2146C72.3486 54.3867 72.4089 46.5518 67.6553 41.6531C67.4735 41.4627 67.2444 41.3246 66.9922 41.2517ZM40.085 41.2351C39.8305 41.171 39.5636 41.1759 39.3115 41.2488C39.0595 41.3217 38.8311 41.46 38.6494 41.6501C33.8922 46.549 33.952 54.3873 38.7832 59.2117C38.9215 59.35 39.0852 59.4604 39.2656 59.5349C39.446 59.6094 39.6399 59.6469 39.835 59.6462C40.0298 59.6455 40.2226 59.6066 40.4023 59.531C40.5822 59.4553 40.7455 59.3441 40.8828 59.2048C41.0203 59.0658 41.129 58.9008 41.2031 58.7195C41.2772 58.538 41.3151 58.3434 41.3145 58.1472C41.3138 57.9511 41.2755 57.7568 41.2002 57.5759C41.1249 57.395 41.0145 57.2307 40.876 57.0925C39.9935 56.212 39.2932 55.1642 38.8164 54.0095C38.3397 52.8549 38.096 51.6162 38.0986 50.366C38.0924 47.8888 39.0546 45.5082 40.7773 43.738C41.1549 43.3582 41.299 42.8006 41.1543 42.2859C41.0845 42.0313 40.9495 41.7995 40.7617 41.615C40.5738 41.4304 40.3397 41.2993 40.085 41.2351ZM61.2949 44.2957C60.9033 44.2887 60.5225 44.4375 60.2402 44.7107C59.9567 44.9853 59.7932 45.3617 59.7852 45.7576C59.7772 46.1535 59.9259 46.537 60.1982 46.823C62.1212 48.8393 62.0825 52.0342 60.1064 54.0007C59.915 54.1836 59.7754 54.4145 59.7021 54.6697C59.6289 54.9249 59.6243 55.1955 59.6895 55.4529C59.7549 55.7098 59.8871 55.9447 60.0723 56.1335C60.2577 56.3225 60.4903 56.4588 60.7451 56.5281C61.2637 56.6664 61.8174 56.514 62.1914 56.1238C65.3181 53.0107 65.3821 47.9486 62.3369 44.7537C62.2023 44.6116 62.0401 44.4973 61.8613 44.4187C61.6827 44.3402 61.4899 44.2986 61.2949 44.2957ZM45.0117 44.2947C44.6183 44.3028 44.2437 44.4676 43.9707 44.7527C40.9254 47.9475 40.9888 53.0096 44.1152 56.1228C44.2537 56.2609 44.4181 56.3708 44.5986 56.4451C44.7792 56.5193 44.9729 56.5564 45.168 56.5554C45.3628 56.5544 45.5557 56.5152 45.7354 56.4392C45.9151 56.3631 46.0787 56.2516 46.2158 56.1121C46.4921 55.8304 46.6464 55.4501 46.6445 55.0544C46.6425 54.6585 46.4844 54.2787 46.2051 53.9998C44.2289 52.0331 44.1899 48.8383 46.1133 46.822C46.3855 46.5361 46.5342 46.1533 46.5264 45.7576C46.5184 45.3617 46.3548 44.9844 46.0713 44.7097C45.787 44.4358 45.4053 44.2867 45.0117 44.2947Z" fill="#3BE7FF"/>
</g>
</g>
<defs>
<filter id="filter0_f_1772_1187" x="27.6328" y="98.4946" width="52.7428" height="51.2349" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8.36428" result="effect1_foregroundBlur_1772_1187"/>
</filter>
<filter id="filter1_f_1772_1187" x="22.2714" y="99.2714" width="63.4571" height="48.4571" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8.36428" result="effect1_foregroundBlur_1772_1187"/>
</filter>
<filter id="filter2_i_1772_1187" x="32.375" y="33.5127" width="40.4678" height="41.8906" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="11"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.82 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1772_1187"/>
</filter>
<filter id="filter3_di_1772_1187" x="-3.84961" y="-2.5" width="114" height="114" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5.25"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.129412 0 0 0 0 0.517647 0 0 0 0 0.972549 0 0 0 0.59 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1772_1187"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1772_1187" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.75"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1772_1187"/>
</filter>
<linearGradient id="paint0_linear_1772_1187" x1="4.67116" y1="101" x2="4.67116" y2="144.848" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE4BB" stop-opacity="0.192157"/>
<stop offset="0.653088" stop-color="#FFE4BB" stop-opacity="0.192157"/>
<stop offset="1" stop-color="#FFE4BB" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint1_linear_1772_1187" x1="0" y1="101" x2="0" y2="149" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFF4DD" stop-opacity="0.384314"/>
<stop offset="0.81374" stop-color="#FFF4DD" stop-opacity="0.01"/>
<stop offset="1" stop-color="#FFF4DD" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint2_linear_1772_1187" x1="32.375" y1="33.5127" x2="32.375" y2="75.4033" gradientUnits="userSpaceOnUse">
<stop stop-color="#DBA75D"/>
<stop offset="1" stop-color="#FFCA59"/>
</linearGradient>
<linearGradient id="paint3_linear_1772_1187" x1="35.6089" y1="33.5127" x2="35.6089" y2="68.7081" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD487"/>
<stop offset="0.487497" stop-color="#FFD487"/>
<stop offset="1" stop-color="#EEB968" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint4_linear_1772_1187" x1="32.375" y1="33.5127" x2="32.375" y2="75.4033" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.01"/>
<stop offset="0.596155" stop-color="white" stop-opacity="0.01"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint5_linear_1772_1187" x1="53.1504" y1="8" x2="53.1504" y2="101" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<clipPath id="clip0_1772_1187">
<rect width="108" height="48" fill="white" transform="translate(0 101)"/>
</clipPath>
<clipPath id="clip1_1772_1187">
<rect width="105.217" height="131.522" fill="white"/>
</clipPath>
<clipPath id="clip2_1772_1187">
<rect width="60.1013" height="60.1013" fill="white" transform="translate(23.0996 24.6624)"/>
</clipPath>
</defs>
</svg>
