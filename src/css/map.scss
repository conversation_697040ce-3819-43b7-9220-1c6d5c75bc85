.map-wrapper.maplibregl-map {
  .maplibregl-popup {

    .maplibregl-popup-tip {
      display: none;
    }
  }
}

// 自定义弹窗样式
.maplibregl-popup.custom-map-popup {
  .maplibregl-popup-content {
    background: url('@/assets/images/dispatch/map/map-popup.png') no-repeat center center;
    background-size: 100% 100%;
    border: none;
    border-radius: 0;
    box-shadow: none;
    padding: 20px;
    min-width: 200px;
    min-height: 100px;

    // 确保内容在背景图片之上
    position: relative;
    z-index: 1;

    // 文字样式
    color: #333;
    font-size: 14px;
    line-height: 1.4;
  }

  // 隐藏默认的箭头
  .maplibregl-popup-tip {
    display: none !important;
  }

  // 关闭按钮样式
  .maplibregl-popup-close-button {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    line-height: 1;
    cursor: pointer;
    z-index: 2;

    &:hover {
      background: rgba(255, 255, 255, 1);
    }
  }
}